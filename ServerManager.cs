using Mirror;
using UnityEngine;

public class ServerManager : NetworkBehaviour
{
    [Header("Server Settings")]
    public string welcomeMessage = "سلام! به سرور خوش آمدید";
    
    void Start()
    {
        // فقط در سمت سرور اجرا شود
        if (!isServer)
            return;
            
        Debug.Log("Server Manager Started - آماده ارسال پیام به کلاینت‌ها");
    }

    // این متد وقتی کلاینت جدیدی متصل می‌شود فراخوانی می‌شود
    public override void OnStartServer()
    {
        Debug.Log("Server started successfully - سرور با موفقیت راه‌اندازی شد");
        
        // گوش دادن به اتصال کلاینت‌های جدید
        NetworkServer.OnConnectedEvent += OnClientConnected;
        NetworkServer.OnDisconnectedEvent += OnClientDisconnected;
    }

    // وقتی کلاینت جدیدی متصل می‌شود
    void OnClientConnected(NetworkConnectionToClient conn)
    {
        Debug.Log($"Client connected with ID: {conn.connectionId} - کلاینت متصل شد");
        
        // ارسال پیام خوش‌آمدگویی به کلاینت جدید
        SendWelcomeMessage(conn);
    }

    // وقتی کلاینت قطع می‌شود
    void OnClientDisconnected(NetworkConnectionToClient conn)
    {
        Debug.Log($"Client disconnected with ID: {conn.connectionId} - کلاینت قطع شد");
    }

    // ارسال پیام خوش‌آمدگویی به کلاینت
    void SendWelcomeMessage(NetworkConnectionToClient targetConnection)
    {
        // ایجاد پیام برای ارسال
        WelcomeMessage message = new WelcomeMessage
        {
            message = welcomeMessage,
            serverTime = System.DateTime.Now.ToString("HH:mm:ss")
        };

        // ارسال پیام به کلاینت مشخص
        targetConnection.Send(message);
        
        Debug.Log($"Welcome message sent to client {targetConnection.connectionId}: {welcomeMessage}");
    }

    // متد برای ارسال پیام به همه کلاینت‌ها
    [Server]
    public void BroadcastMessageToAllClients(string message)
    {
        WelcomeMessage broadcastMessage = new WelcomeMessage
        {
            message = message,
            serverTime = System.DateTime.Now.ToString("HH:mm:ss")
        };

        // ارسال به همه کلاینت‌های متصل
        NetworkServer.SendToAll(broadcastMessage);
        Debug.Log($"Broadcast message sent to all clients: {message}");
    }

    void OnDestroy()
    {
        // پاک کردن event listener ها
        if (NetworkServer.active)
        {
            NetworkServer.OnConnectedEvent -= OnClientConnected;
            NetworkServer.OnDisconnectedEvent -= OnClientDisconnected;
        }
    }
}

// کلاس پیام برای ارسال از سرور به کلاینت
public struct WelcomeMessage : NetworkMessage
{
    public string message;
    public string serverTime;
}
