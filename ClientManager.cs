using Mirror;
using UnityEngine;
using UnityEngine.UI;

public class ClientManager : NetworkBehaviour
{
    [Header("UI Elements")]
    public Text messageDisplayText; // برای نمایش پیام‌های دریافتی
    public Text connectionStatusText; // برای نمایش وضعیت اتصال
    
    [Header("Client Settings")]
    public bool autoConnect = true;
    public string serverAddress = "localhost";
    
    private NetworkManager networkManager;

    void Start()
    {
        // فقط در سمت کلاینت اجرا شود
        if (isServer)
            return;
            
        networkManager = NetworkManager.singleton;
        
        // تنظیم UI اولیه
        UpdateConnectionStatus("در حال اتصال...");
        
        Debug.Log("Client Manager Started - مدیر کلاینت راه‌اندازی شد");
        
        // اتصال خودکار اگر فعال باشد
        if (autoConnect && !NetworkClient.isConnected)
        {
            ConnectToServer();
        }
    }

    public override void OnStartClient()
    {
        Debug.Log("Client started - کلاینت راه‌اندازی شد");
        
        // ثبت listener برای دریافت پیام‌های خوش‌آمدگویی
        NetworkClient.RegisterHandler<WelcomeMessage>(OnWelcomeMessageReceived);
        
        UpdateConnectionStatus("در حال اتصال به سرور...");
    }

    // متد برای اتصال به سرور
    public void ConnectToServer()
    {
        if (NetworkClient.isConnected)
        {
            Debug.Log("Already connected to server - قبلاً به سرور متصل شده");
            return;
        }

        networkManager.networkAddress = serverAddress;
        networkManager.StartClient();
        
        Debug.Log($"Attempting to connect to server at {serverAddress} - تلاش برای اتصال به سرور");
        UpdateConnectionStatus("در حال اتصال...");
    }

    // متد برای قطع اتصال از سرور
    public void DisconnectFromServer()
    {
        if (NetworkClient.isConnected)
        {
            networkManager.StopClient();
            Debug.Log("Disconnected from server - از سرور قطع شد");
            UpdateConnectionStatus("قطع شده");
        }
    }

    // وقتی پیام خوش‌آمدگویی از سرور دریافت می‌شود
    void OnWelcomeMessageReceived(WelcomeMessage message)
    {
        Debug.Log($"Welcome message received: {message.message} at {message.serverTime}");
        
        // نمایش پیام در UI
        DisplayMessage($"پیام از سرور ({message.serverTime}):\n{message.message}");
        
        // به‌روزرسانی وضعیت اتصال
        UpdateConnectionStatus("متصل شده ✓");
    }

    // نمایش پیام در UI
    void DisplayMessage(string message)
    {
        if (messageDisplayText != null)
        {
            messageDisplayText.text = message;
        }
        else
        {
            Debug.Log($"Message to display: {message}");
        }
    }

    // به‌روزرسانی وضعیت اتصال در UI
    void UpdateConnectionStatus(string status)
    {
        if (connectionStatusText != null)
        {
            connectionStatusText.text = $"وضعیت: {status}";
        }
        
        Debug.Log($"Connection Status: {status}");
    }

    // Event های اتصال شبکه
    public override void OnStartLocalPlayer()
    {
        Debug.Log("Local player started - بازیکن محلی راه‌اندازی شد");
    }

    // وقتی کلاینت با موفقیت به سرور متصل می‌شود
    void OnClientConnect()
    {
        Debug.Log("Successfully connected to server - با موفقیت به سرور متصل شد");
        UpdateConnectionStatus("متصل شده");
    }

    // وقتی کلاینت از سرور قطع می‌شود
    void OnClientDisconnect()
    {
        Debug.Log("Disconnected from server - از سرور قطع شد");
        UpdateConnectionStatus("قطع شده");
        DisplayMessage("اتصال به سرور قطع شد");
    }

    // وقتی خطا در اتصال رخ می‌دهد
    void OnClientError()
    {
        Debug.LogError("Client connection error - خطا در اتصال کلاینت");
        UpdateConnectionStatus("خطا در اتصال");
        DisplayMessage("خطا در اتصال به سرور");
    }

    void OnDestroy()
    {
        // پاک کردن handler ها
        if (NetworkClient.isConnected)
        {
            NetworkClient.UnregisterHandler<WelcomeMessage>();
        }
    }

    // متدهای UI برای دکمه‌ها
    public void OnConnectButtonClick()
    {
        ConnectToServer();
    }

    public void OnDisconnectButtonClick()
    {
        DisconnectFromServer();
    }
}
